# 统一认证授权系统 PRD

## 1. 产品概述

### 1.1 产品背景
随着企业数字化转型的深入，企业内部系统数量不断增加，用户需要在多个系统间切换，传统的分散式认证方式带来了以下问题：
- 用户需要记住多套账号密码
- 安全风险分散，难以统一管控
- 用户权限管理复杂，缺乏统一视图
- 缺乏统一的安全策略和审计能力

### 1.2 产品定位
构建一个企业级统一认证授权系统，支持多租户架构，提供安全、便捷、可扩展的身份认证和权限管理服务。

### 1.3 核心价值
- **统一身份管理**：一套账号体系，多系统单点登录
- **多租户支持**：支持企业多组织架构管理
- **安全可控**：多因子认证，细粒度权限控制
- **易于集成**：标准协议支持，快速接入第三方系统

## 2. 系统架构图

### 2.1 整体架构设计

```mermaid
graph TB
    subgraph "前端层"
        WEB[Web管理后台]
        MOBILE[移动端应用]
        API_GW[API网关]
    end

    subgraph "应用层"
        AUTH[认证服务]
        USER[用户管理服务]
        TENANT[租户管理服务]
        PERM[权限管理服务]
        SSO[单点登录服务]
        MFA[多因子认证服务]
    end

    subgraph "数据层"
        USER_DB[(用户数据库)]
        TENANT_DB[(租户数据库)]
        PERM_DB[(权限数据库)]
        LOG_DB[(审计日志)]
        CACHE[(Redis缓存)]
    end

    subgraph "外部服务"
        EMAIL[邮件服务]
        SMS[短信服务]
        LDAP[LDAP/AD]
        THIRD_PARTY[第三方应用]
    end

    WEB --> API_GW
    MOBILE --> API_GW
    API_GW --> AUTH
    API_GW --> USER
    API_GW --> TENANT
    API_GW --> PERM

    AUTH --> USER_DB
    AUTH --> CACHE
    AUTH --> MFA

    USER --> USER_DB
    USER --> EMAIL
    USER --> SMS

    TENANT --> TENANT_DB
    TENANT --> USER_DB

    PERM --> PERM_DB
    PERM --> CACHE

    SSO --> AUTH
    SSO --> PERM
    SSO --> THIRD_PARTY

    MFA --> EMAIL
    MFA --> SMS
    MFA --> CACHE

    AUTH --> LOG_DB
    USER --> LOG_DB
    TENANT --> LOG_DB
    PERM --> LOG_DB

    USER --> LDAP

    style WEB fill:#e3f2fd
    style MOBILE fill:#e3f2fd
    style AUTH fill:#fff3e0
    style USER fill:#fff3e0
    style TENANT fill:#fff3e0
    style PERM fill:#fff3e0
    style SSO fill:#fff3e0
    style MFA fill:#fff3e0
```

### 2.2 系统数据流图

```mermaid
graph LR
    subgraph "用户交互层"
        U1[Web用户]
        U2[移动用户]
        U3[API用户]
    end

    subgraph "接入层"
        LB[负载均衡器]
        GW[API网关]
    end

    subgraph "业务服务层"
        AS[认证服务]
        US[用户服务]
        TS[租户服务]
        PS[权限服务]
        SS[SSO服务]
        MS[MFA服务]
        NS[通知服务]
    end

    subgraph "数据存储层"
        RDB[(关系数据库)]
        CACHE[(Redis缓存)]
        LOG[(日志存储)]
        FILE[(文件存储)]
    end

    subgraph "外部集成"
        MAIL[邮件网关]
        SMS_GW[短信网关]
        LDAP_SRV[LDAP服务]
        APP[第三方应用]
    end

    %% 用户请求流
    U1 --> LB
    U2 --> LB
    U3 --> LB
    LB --> GW

    %% API网关路由
    GW --> AS
    GW --> US
    GW --> TS
    GW --> PS
    GW --> SS

    %% 服务间调用
    AS --> MS
    AS --> US
    AS --> PS
    US --> TS
    US --> NS
    TS --> PS
    SS --> AS
    SS --> PS
    MS --> NS

    %% 数据访问
    AS --> RDB
    AS --> CACHE
    US --> RDB
    US --> FILE
    TS --> RDB
    PS --> RDB
    PS --> CACHE
    SS --> CACHE
    MS --> CACHE

    %% 日志记录
    AS --> LOG
    US --> LOG
    TS --> LOG
    PS --> LOG
    SS --> LOG
    GW --> LOG

    %% 外部服务调用
    NS --> MAIL
    NS --> SMS_GW
    US --> LDAP_SRV
    SS --> APP

    %% 数据流标注
    AS -.->|用户认证信息| RDB
    PS -.->|权限缓存| CACHE
    SS -.->|会话状态| CACHE
    MS -.->|验证码| CACHE
    US -.->|用户头像| FILE

    style U1 fill:#e3f2fd
    style U2 fill:#e3f2fd
    style U3 fill:#e3f2fd
    style AS fill:#fff3e0
    style US fill:#fff3e0
    style TS fill:#fff3e0
    style PS fill:#fff3e0
    style SS fill:#fff3e0
    style MS fill:#fff3e0
    style RDB fill:#f3e5f5
    style CACHE fill:#e8f5e8
    style LOG fill:#fff8e1
```

## 3. 核心业务流程图

### 3.1 用户注册与租户加入流程

```mermaid
flowchart TD
    A[用户访问注册页面] --> B[填写基础信息]
    B --> C[邮箱/手机验证]
    C --> D{验证成功?}
    D -->|否| C
    D -->|是| E[创建用户账户]
    E --> F[设置密码]
    F --> G[配置MFA可选]
    G --> H[注册完成]

    H --> I[收到租户邀请]
    I --> J[点击邀请链接]
    J --> K{用户已存在?}
    K -->|否| L[引导注册]
    K -->|是| M[验证身份]
    L --> M
    M --> N[加入租户]
    N --> O[分配默认角色]
    O --> P[发送欢迎通知]

    style A fill:#e1f5fe
    style H fill:#c8e6c9
    style P fill:#c8e6c9
```

### 3.2 用户登录与MFA认证流程

```mermaid
flowchart TD
    A[用户访问登录页面] --> B[输入用户名/密码]
    B --> C[验证用户凭据]
    C --> D{凭据正确?}
    D -->|否| E[显示错误信息]
    E --> F{重试次数超限?}
    F -->|是| G[账户临时锁定]
    F -->|否| B

    D -->|是| H{是否启用MFA?}
    H -->|否| I[生成访问令牌]
    H -->|是| J[发送MFA验证码]
    J --> K[用户输入验证码]
    K --> L{验证码正确?}
    L -->|否| M[显示错误]
    M --> N{重试次数超限?}
    N -->|是| O[MFA验证锁定]
    N -->|否| K
    L -->|是| I

    I --> P[记录登录日志]
    P --> Q{多租户用户?}
    Q -->|否| R[直接进入系统]
    Q -->|是| S[显示租户选择]
    S --> T[用户选择租户]
    T --> U[切换租户上下文]
    U --> V[加载租户权限]
    V --> R

    style A fill:#e1f5fe
    style R fill:#c8e6c9
    style G fill:#ffcdd2
    style O fill:#ffcdd2
```

### 3.3 租户管理员邀请用户流程

```mermaid
flowchart TD
    A[租户管理员登录] --> B[进入用户管理页面]
    B --> C[点击邀请用户]
    C --> D[填写邀请信息]
    D --> E[选择用户角色]
    E --> F[设置权限范围]
    F --> G[发送邀请]

    G --> H[系统检查用户]
    H --> I{用户已存在?}
    I -->|是| J[检查是否已在租户]
    I -->|否| K[创建邀请记录]

    J --> L{已在租户?}
    L -->|是| M[提示用户已存在]
    L -->|否| N[添加到租户]

    K --> O[生成邀请链接]
    O --> P[发送邀请邮件]
    P --> Q[记录邀请日志]

    N --> R[更新用户角色]
    R --> S[发送通知]
    S --> T[记录操作日志]

    Q --> U[等待用户响应]
    U --> V{用户接受邀请?}
    V -->|是| W[用户加入租户]
    V -->|否| X[邀请过期/拒绝]

    W --> Y[分配角色权限]
    Y --> Z[发送欢迎消息]

    style A fill:#e1f5fe
    style Z fill:#c8e6c9
    style M fill:#fff3e0
    style X fill:#ffcdd2
```

### 3.4 用户申请加入租户流程

```mermaid
flowchart TD
    A[用户浏览租户列表] --> B[选择目标租户]
    B --> C[点击申请加入]
    C --> D[填写申请信息]
    D --> E[提交申请]
    E --> F[系统检查用户状态]

    F --> G{用户已在租户?}
    G -->|是| H[提示已在租户中]
    G -->|否| I[创建申请记录]

    I --> J[发送申请通知给管理员]
    J --> K[等待管理员审批]

    K --> L{管理员审批结果}
    L -->|同意| M[用户加入租户]
    L -->|拒绝| N[发送拒绝通知]
    L -->|超时| O[申请自动过期]

    M --> P[分配默认角色]
    P --> Q[发送加入成功通知]

    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style H fill:#fff3e0
    style N fill:#ffcdd2
    style O fill:#ffcdd2
```

### 3.5 用户创建租户流程

```mermaid
flowchart TD
    A[用户登录系统] --> B[点击创建租户]
    B --> C[填写租户基础信息]
    C --> D[设置租户域名]
    D --> E[选择租户类型]
    E --> F[配置初始设置]
    F --> G[提交创建申请]

    G --> H[系统验证信息]
    H --> I{信息验证通过?}
    I -->|否| J[显示错误信息]
    J --> C

    I -->|是| K[检查域名唯一性]
    K --> L{域名可用?}
    L -->|否| M[提示域名已存在]
    M --> D

    L -->|是| N[创建租户记录]
    N --> O[设置用户为租户管理员]
    O --> P[初始化租户权限]
    P --> Q[创建默认角色]
    Q --> R[发送创建成功通知]
    R --> S[跳转到租户管理页面]

    style A fill:#e1f5fe
    style S fill:#c8e6c9
    style J fill:#ffcdd2
    style M fill:#ffcdd2
```

### 3.6 用户确认邀请流程

```mermaid
flowchart TD
    A[用户收到邀请邮件] --> B[点击邀请链接]
    B --> C[验证邀请链接有效性]
    C --> D{链接有效?}
    D -->|否| E[显示链接无效/过期]

    D -->|是| F[显示邀请详情]
    F --> G[租户信息展示]
    G --> H[角色权限说明]
    H --> I[用户选择操作]

    I --> J{用户决定}
    J -->|接受| K[确认加入租户]
    J -->|拒绝| L[拒绝邀请]
    J -->|忽略| M[邀请保持待处理状态]

    K --> N[更新邀请状态为已接受]
    N --> O[添加用户到租户]
    O --> P[分配指定角色]
    P --> Q[发送欢迎通知]
    Q --> R[记录操作日志]

    L --> S[更新邀请状态为已拒绝]
    S --> T[通知邀请人]
    T --> U[记录拒绝日志]

    style A fill:#e1f5fe
    style R fill:#c8e6c9
    style U fill:#c8e6c9
    style E fill:#ffcdd2
```

### 3.7 完整的租户加入方式总览

```mermaid
graph TD
    subgraph "用户创建租户"
        A1[用户登录] --> A2[创建租户]
        A2 --> A3[填写租户信息]
        A3 --> A4[验证域名唯一性]
        A4 --> A5[创建成功]
        A5 --> A6[自动成为管理员]
    end

    subgraph "管理员邀请用户"
        B1[管理员发送邀请] --> B2[用户收到邮件]
        B2 --> B3[用户点击链接]
        B3 --> B4{用户选择}
        B4 -->|接受| B5[加入租户]
        B4 -->|拒绝| B6[拒绝邀请]
        B4 -->|忽略| B7[邀请过期]
    end

    subgraph "用户申请加入"
        C1[用户浏览租户] --> C2[提交申请]
        C2 --> C3[管理员收到通知]
        C3 --> C4{管理员审批}
        C4 -->|同意| C5[用户加入租户]
        C4 -->|拒绝| C6[申请被拒绝]
        C4 -->|超时| C7[申请过期]
    end

    subgraph "开放注册(可选)"
        D1[用户注册] --> D2[选择租户]
        D2 --> D3[直接加入]
    end

    A6 --> E[租户管理]
    B5 --> E
    C5 --> E
    D3 --> E

    style A6 fill:#c8e6c9
    style B5 fill:#c8e6c9
    style C5 fill:#c8e6c9
    style D3 fill:#c8e6c9
    style B6 fill:#ffcdd2
    style B7 fill:#ffcdd2
    style C6 fill:#ffcdd2
    style C7 fill:#ffcdd2
```

### 3.8 单点登录(SSO)认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant A as 应用系统
    participant S as 认证中心
    participant T as 令牌服务

    U->>A: 1. 访问受保护资源
    A->>A: 2. 检查本地会话
    A->>S: 3. 重定向到认证中心
    S->>S: 4. 检查SSO会话

    alt 未登录
        S->>U: 5. 显示登录页面
        U->>S: 6. 提交登录凭据
        S->>S: 7. 验证用户身份

        alt 启用MFA
            S->>U: 8. 要求MFA验证
            U->>S: 9. 提交MFA验证码
            S->>S: 10. 验证MFA
        end

        S->>S: 11. 创建SSO会话
    end

    S->>T: 12. 生成授权码
    T->>S: 13. 返回授权码
    S->>A: 14. 重定向回应用(带授权码)
    A->>T: 15. 用授权码换取令牌
    T->>T: 16. 验证授权码
    T->>A: 17. 返回访问令牌
    A->>A: 18. 创建本地会话
    A->>U: 19. 返回受保护资源

    Note over U,T: 后续访问其他应用时，可直接使用SSO会话，无需重新登录
```

### 3.9 权限验证与授权流程

```mermaid
flowchart TD
    A[用户请求访问资源] --> B[提取访问令牌]
    B --> C{令牌有效?}
    C -->|否| D[返回401未授权]
    C -->|是| E[解析用户身份]
    E --> F[获取当前租户上下文]
    F --> G[查询用户在租户中的角色]
    G --> H[获取角色权限列表]
    H --> I[检查资源访问权限]
    I --> J{权限匹配?}
    J -->|否| K[返回403禁止访问]
    J -->|是| L[记录访问日志]
    L --> M[允许访问资源]

    subgraph "权限缓存机制"
        N[检查权限缓存]
        O[缓存命中]
        P[缓存未命中]
        Q[查询数据库]
        R[更新缓存]
    end

    H --> N
    N --> O
    N --> P
    P --> Q
    Q --> R
    R --> I
    O --> I

    subgraph "动态权限检查"
        S[基础权限检查]
        T[资源级权限检查]
        U[时间窗口检查]
        V[IP白名单检查]
    end

    I --> S
    S --> T
    T --> U
    U --> V
    V --> J

    style A fill:#e1f5fe
    style M fill:#c8e6c9
    style D fill:#ffcdd2
    style K fill:#ffcdd2
```

## 4. 用户角色定义

### 4.1 系统管理员（Super Admin）
- 系统最高权限管理者
- 负责租户创建、删除和基础配置
- 系统级监控和运维

### 4.2 租户管理员（Tenant Admin）
- 租户内最高权限管理者
- 负责租户内用户管理、角色权限配置
- 租户级别的安全策略设置

### 4.3 普通用户（End User）
- 系统的最终使用者
- 可以加入多个租户
- 在不同租户中可能拥有不同角色和权限

## 5. 核心功能需求

### 5.1 用户管理
#### 5.1.1 用户注册
- 支持邮箱/手机号注册
- 邮箱/短信验证
- 密码强度校验
- 用户基础信息收集

#### 3.1.2 用户认证
- 用户名/邮箱/手机号登录
- 密码认证
- 多因子认证（MFA）
  - TOTP（Time-based One-Time Password）
  - SMS验证码
  - 邮箱验证码
  - 硬件令牌（可选）

#### 3.1.3 用户资料管理
- 个人信息维护
- 密码修改
- MFA设备管理
- 登录历史查看

### 5.2 租户管理
#### 5.2.1 租户创建与配置
- **用户自主创建租户**
  - 租户基础信息设置（名称、描述、联系方式）
  - 租户域名配置（唯一性验证）
  - 租户类型选择（企业版/标准版/试用版）
  - 创建者自动成为租户管理员
- **租户品牌定制**
  - 自定义Logo和主题色彩
  - 登录页面定制
  - 邮件模板定制
- **租户级安全策略**
  - 密码策略配置
  - 会话超时设置
  - IP白名单管理

#### 5.2.2 租户用户管理
- **多种加入方式**
  - 管理员邀请用户（需用户确认）
  - 用户申请加入（需管理员审批）
  - 开放注册（可选，需管理员开启）
- **邀请确认机制**
  - 邮件邀请链接
  - 邀请详情展示
  - 用户可接受/拒绝邀请
  - 邀请过期机制
- **申请审批流程**
  - 用户提交加入申请
  - 管理员审批/拒绝
  - 申请状态跟踪
  - 自动过期处理
- **用户状态管理**
  - 用户角色分配
  - 用户状态管理（激活/禁用/移除）
  - 批量用户操作
  - 用户权限变更记录

### 5.3 权限管理
#### 5.3.1 角色管理
- 预定义角色模板
- 自定义角色创建
- 角色权限配置
- 角色继承关系

#### 5.3.2 权限控制
- 基于角色的访问控制（RBAC）
- 资源级权限控制
- 动态权限验证
- 权限审计日志

### 5.4 单点登录（SSO）
#### 5.4.1 协议支持
- SAML 2.0
- OAuth 2.0 / OpenID Connect
- CAS协议
- 自定义API接口

#### 5.4.2 应用集成
- 应用注册管理
- 回调地址配置
- 令牌管理
- 会话管理

## 6. 技术架构需求

### 6.1 系统架构
- 微服务架构设计
- 高可用部署
- 水平扩展能力
- 容器化部署支持

### 6.2 数据存储
- 用户数据加密存储
- 敏感信息脱敏
- 数据备份与恢复
- 多数据中心同步

### 6.3 安全要求
- 传输层安全（TLS 1.3）
- 密码加密存储（bcrypt/Argon2）
- 会话安全管理
- 防暴力破解机制
- 安全审计日志

## 7. 非功能性需求

### 7.1 性能要求
- 登录响应时间 < 2秒
- 权限验证响应时间 < 500ms
- 支持并发用户数 > 10,000
- 系统可用性 > 99.9%

### 7.2 扩展性要求
- 支持水平扩展
- 插件化架构
- API优先设计
- 多语言支持

### 7.3 合规要求
- GDPR合规
- 等保三级要求
- SOX审计支持
- 数据本地化存储

## 8. 用户体验设计

### 8.1 界面设计原则
- 简洁直观的用户界面
- 响应式设计，支持多终端
- 无障碍访问支持
- 品牌定制能力

### 8.2 用户流程优化
- 简化注册流程
- 智能登录提示
- 友好的错误提示
- 渐进式功能引导

## 9. 实施计划

### 9.1 第一阶段（MVP）
- 基础用户管理
- 简单租户管理
- 基础权限控制
- 基础SSO支持

### 9.2 第二阶段
- MFA认证
- 高级权限管理
- 审计日志
- 管理后台完善

### 9.3 第三阶段
- 高级安全特性
- 企业级集成
- 性能优化
- 监控告警

## 10. 风险评估

### 10.1 技术风险
- 高并发场景下的性能瓶颈
- 数据一致性问题
- 第三方系统集成复杂度

### 10.2 安全风险
- 身份认证绕过
- 权限提升攻击
- 数据泄露风险

### 10.3 业务风险
- 用户接受度
- 迁移成本
- 运维复杂度

## 11. 成功指标

### 11.1 技术指标
- 系统响应时间
- 系统可用性
- 错误率
- 并发处理能力

### 11.2 业务指标
- 用户活跃度
- 登录成功率
- 用户满意度
- 安全事件数量

## 12. 详细业务流程

### 12.1 用户生命周期管理

#### 用户状态定义
- **待激活**: 用户已注册但未完成邮箱/手机验证
- **正常**: 用户可正常使用系统
- **锁定**: 因安全原因被临时锁定
- **禁用**: 管理员主动禁用
- **注销**: 用户主动注销账户

#### 状态转换规则
```
待激活 → 正常: 完成邮箱/手机验证
正常 → 锁定: 多次登录失败/安全策略触发
锁定 → 正常: 管理员解锁/自动解锁时间到期
正常 → 禁用: 管理员操作
禁用 → 正常: 管理员重新启用
任意状态 → 注销: 用户主动注销
```

### 12.2 租户权限模型

#### 权限层级结构
```
租户级权限
├── 用户管理
│   ├── 查看用户列表
│   ├── 邀请用户
│   ├── 编辑用户信息
│   ├── 禁用/启用用户
│   └── 删除用户
├── 角色管理
│   ├── 查看角色列表
│   ├── 创建角色
│   ├── 编辑角色
│   └── 删除角色
├── 应用管理
│   ├── 查看应用列表
│   ├── 注册应用
│   ├── 配置应用
│   └── 删除应用
└── 租户设置
    ├── 基础信息设置
    ├── 安全策略配置
    ├── 品牌定制
    └── 审计日志查看
```

#### 预定义角色
- **租户管理员**: 拥有租户内所有权限
- **用户管理员**: 负责用户和角色管理
- **应用管理员**: 负责应用集成和配置
- **审计员**: 只读权限，可查看审计日志
- **普通用户**: 基础使用权限

### 12.3 安全策略配置

#### 密码策略
- 最小长度: 8-32字符
- 复杂度要求: 大小写字母+数字+特殊字符
- 历史密码检查: 不能与最近5次密码相同
- 密码有效期: 90天（可配置）
- 密码重试限制: 5次失败后锁定

#### 会话策略
- 会话超时: 8小时（可配置）
- 并发会话限制: 3个（可配置）
- 异地登录检测: 启用地理位置验证
- 设备绑定: 可选择信任设备

#### MFA策略
- 强制启用条件: 管理员角色/敏感操作
- 支持的MFA方式: TOTP/SMS/邮箱
- 备用恢复码: 10个一次性恢复码
- MFA重置: 需要管理员审批

## 13. API接口规范

### 13.1 认证接口

#### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123",
  "tenant_id": "optional_tenant_id",
  "mfa_code": "optional_mfa_code"
}

Response:
{
  "access_token": "jwt_token",
  "refresh_token": "refresh_token",
  "expires_in": 3600,
  "user_info": {
    "id": "user_id",
    "username": "<EMAIL>",
    "name": "用户姓名",
    "tenants": ["tenant1", "tenant2"]
  }
}
```

#### 令牌刷新
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "refresh_token"
}
```

#### 用户注销
```http
POST /api/v1/auth/logout
Authorization: Bearer {access_token}
```

### 13.2 用户管理接口

#### 用户注册
```http
POST /api/v1/users/register
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123",
  "name": "用户姓名",
  "phone": "13800138000"
}
```

#### 获取用户信息
```http
GET /api/v1/users/profile
Authorization: Bearer {access_token}
```

#### 更新用户信息
```http
PUT /api/v1/users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "新姓名",
  "phone": "新手机号"
}
```

### 13.3 租户管理接口

#### 创建租户
```http
POST /api/v1/tenants
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "租户名称",
  "domain": "tenant.example.com",
  "description": "租户描述"
}
```

#### 邀请用户加入租户
```http
POST /api/v1/tenants/{tenant_id}/invitations
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role": "user",
  "message": "邀请消息"
}
```

#### 用户申请加入租户
```http
POST /api/v1/tenants/{tenant_id}/applications
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "message": "申请理由",
  "expected_role": "user"
}
```

#### 处理用户申请
```http
PUT /api/v1/tenants/{tenant_id}/applications/{application_id}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "action": "approve", // approve | reject
  "role": "user",
  "message": "处理说明"
}
```

#### 用户确认邀请
```http
PUT /api/v1/invitations/{invitation_id}/confirm
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "action": "accept" // accept | reject
}
```

#### 获取用户的邀请列表
```http
GET /api/v1/users/invitations
Authorization: Bearer {access_token}

Response:
{
  "invitations": [
    {
      "id": "invitation_id",
      "tenant": {
        "id": "tenant_id",
        "name": "租户名称",
        "domain": "tenant.example.com"
      },
      "role": "user",
      "invited_by": "邀请人姓名",
      "invited_at": "2024-01-01T00:00:00Z",
      "expires_at": "2024-01-08T00:00:00Z",
      "status": "pending"
    }
  ]
}
```

#### 获取租户申请列表
```http
GET /api/v1/tenants/{tenant_id}/applications
Authorization: Bearer {access_token}

Response:
{
  "applications": [
    {
      "id": "application_id",
      "user": {
        "id": "user_id",
        "name": "用户姓名",
        "email": "<EMAIL>"
      },
      "message": "申请理由",
      "expected_role": "user",
      "applied_at": "2024-01-01T00:00:00Z",
      "status": "pending"
    }
  ]
}
```

## 14. 数据库设计

### 14.1 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100),
    avatar_url VARCHAR(500),
    status ENUM('pending', 'active', 'locked', 'disabled', 'deleted') DEFAULT 'pending',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(255),
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 租户表 (tenants)
```sql
CREATE TABLE tenants (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    description TEXT,
    logo_url VARCHAR(500),
    status ENUM('active', 'suspended', 'deleted') DEFAULT 'active',
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 用户租户关系表 (user_tenants)
```sql
CREATE TABLE user_tenants (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    tenant_id VARCHAR(36) NOT NULL,
    role VARCHAR(50) NOT NULL,
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    invited_by VARCHAR(36),
    invited_at TIMESTAMP,
    joined_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    UNIQUE KEY unique_user_tenant (user_id, tenant_id)
);
```

#### 邀请表 (invitations)
```sql
CREATE TABLE invitations (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    inviter_id VARCHAR(36) NOT NULL,
    email VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    message TEXT,
    status ENUM('pending', 'accepted', 'rejected', 'expired') DEFAULT 'pending',
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (inviter_id) REFERENCES users(id),
    INDEX idx_email_status (email, status),
    INDEX idx_tenant_status (tenant_id, status)
);
```

#### 申请表 (applications)
```sql
CREATE TABLE applications (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    message TEXT,
    expected_role VARCHAR(50) NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'expired') DEFAULT 'pending',
    processed_by VARCHAR(36) NULL,
    processed_at TIMESTAMP NULL,
    process_message TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (processed_by) REFERENCES users(id),
    UNIQUE KEY unique_user_tenant_pending (user_id, tenant_id, status),
    INDEX idx_tenant_status (tenant_id, status)
);
```

#### 角色表 (roles)
```sql
CREATE TABLE roles (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSON,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);
```

## 15. 附录

### 15.1 术语表
- **SSO**: Single Sign-On，单点登录
- **MFA**: Multi-Factor Authentication，多因子认证
- **RBAC**: Role-Based Access Control，基于角色的访问控制
- **SAML**: Security Assertion Markup Language，安全断言标记语言
- **OAuth**: Open Authorization，开放授权
- **OIDC**: OpenID Connect，基于OAuth 2.0的身份认证协议
- **JWT**: JSON Web Token，JSON网络令牌
- **TOTP**: Time-based One-Time Password，基于时间的一次性密码

### 15.2 参考标准
- RFC 6749: OAuth 2.0 Authorization Framework
- RFC 7519: JSON Web Token (JWT)
- RFC 6238: TOTP: Time-Based One-Time Password Algorithm
- SAML 2.0 Specification
- OpenID Connect Core 1.0
